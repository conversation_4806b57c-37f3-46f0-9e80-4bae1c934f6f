import pandas as pd
import numpy as np
import os
import joblib
from pathlib import Path
import matplotlib.pyplot as plt

def extract_rmse_results(data_types, model_types, time_intervals_dict):
    """
    提取每个模型在每个数据集上30分钟和60分钟的平均RMSE值
    
    参数:
    data_types: 数据集类型列表
    model_types: 模型类型列表
    time_intervals_dict: 字典，包含每个数据集的时间间隔
    """
    
    results_summary = {}
    
    for data_type in data_types:
        results_summary[data_type] = {}
        time_interval = time_intervals_dict.get(data_type, 5)  # 默认5分钟
        
        for model_type in model_types:
            # 确定结果文件路径模式
            if model_type == 'ARIMA':
                result_path_pattern = f'result/one_step/{data_type}_150minutes/{{patient}}_resample/{model_type}.pkl'
            else:
                result_path_pattern = f'result/one_step/{data_type}_150minutes/{{patient}}/{model_type}.pkl'
            
            # 收集所有患者的RMSE值
            all_rmse_30min = []
            all_rmse_60min = []
            
            # 查找所有患者的结果文件
            base_dir = Path(result_path_pattern.split('{patient}')[0].format(patient=''))
            if base_dir.exists():
                for patient_dir in base_dir.iterdir():
                    if patient_dir.is_dir():
                        result_file = patient_dir / f'{model_type}.pkl'
                        if result_file.exists():
                            try:
                                # 加载结果
                                results = joblib.load(result_file)
                                rmse_values = results[0]  # RMSE是结果列表的第一个元素
                                
                                # 根据实际的time_step配置来提取RMSE
                                # 从classic_model.py可以看出，实际使用的time_step是[2,4]，经过减1后变成[1,3]
                                # 但保存的结果只有对应这些time_step的值

                                # 对于不同时间间隔，我们需要映射到实际的索引
                                if time_interval == 15:
                                    # 15分钟间隔：time_step=[2,4] -> [1,3] -> 对应15min和45min
                                    # 索引0对应15分钟，索引1对应45分钟
                                    if len(rmse_values) > 0:
                                        all_rmse_30min.append(rmse_values[0])  # 15分钟作为30分钟的近似
                                    if len(rmse_values) > 1:
                                        all_rmse_60min.append(rmse_values[1])  # 45分钟作为60分钟的近似
                                elif time_interval == 5:
                                    # 5分钟间隔：需要检查实际的time_step配置
                                    # 假设也是[2,4] -> [1,3] -> 对应5min和15min
                                    if len(rmse_values) > 0:
                                        all_rmse_30min.append(rmse_values[0])  # 5分钟
                                    if len(rmse_values) > 1:
                                        all_rmse_60min.append(rmse_values[1])  # 15分钟
                                elif time_interval == 3:
                                    # 3分钟间隔：time_step=[2,4] -> [1,3] -> 对应3min和9min
                                    if len(rmse_values) > 0:
                                        all_rmse_30min.append(rmse_values[0])  # 3分钟
                                    if len(rmse_values) > 1:
                                        all_rmse_60min.append(rmse_values[1])  # 9分钟
                                    
                            except Exception as e:
                                print(f"Error loading {result_file}: {e}")
                                continue
            
            # 计算平均值
            avg_rmse_30min = np.mean(all_rmse_30min) if all_rmse_30min else np.nan
            avg_rmse_60min = np.mean(all_rmse_60min) if all_rmse_60min else np.nan
            
            results_summary[data_type][model_type] = {
                '30min_rmse': avg_rmse_30min,
                '60min_rmse': avg_rmse_60min,
                'patient_count_30min': len(all_rmse_30min),
                'patient_count_60min': len(all_rmse_60min),
                'time_interval': time_interval  # 保存时间间隔信息
            }
            
            print(f"{data_type} (间隔{time_interval}min) - {model_type}: "
                  f"30min RMSE = {avg_rmse_30min:.3f}, 60min RMSE = {avg_rmse_60min:.3f}")
    
    return results_summary

def save_results_to_csv(results_summary, output_file='model_rmse_summary.csv'):
    """将结果保存到CSV文件"""
    rows = []
    
    for data_type, models in results_summary.items():
        for model_type, metrics in models.items():
            rows.append({
                'Dataset': data_type,
                'Model': model_type,
                'Time_Interval(min)': metrics['time_interval'],
                '30min_RMSE': metrics['30min_rmse'],
                '60min_RMSE': metrics['60min_rmse'],
                'Patient_Count_30min': metrics['patient_count_30min'],
                'Patient_Count_60min': metrics['patient_count_60min']
            })
    
    df = pd.DataFrame(rows)
    df.to_csv(output_file, index=False)
    print(f"结果已保存到 {output_file}")
    return df

# 使用示例
if __name__ == '__main__':
    # 配置参数 - 包含每个数据集的时间间隔
    data_types = ['Simulated_adolescent', 'Simulator_adult', 'Simulator_child', 
                 'Shanghai_T1DM', 'Shanghai_T2DM', 'ohio_data']
    
    # 定义每个数据集的时间间隔（分钟）
    time_intervals_dict = {
        'Simulated_adolescent': 3,
        'Simulator_adult': 3,
        'Simulator_child': 3,
        'Shanghai_T1DM': 15,
        'Shanghai_T2DM': 15,
        'ohio_data': 5           
    }
    
    model_types = ['SVR', 'Ridge', 'RF', 'KNN', 'LR']
    
    # 提取结果
    print("开始提取RMSE结果...")
    results = extract_rmse_results(data_types, model_types, time_intervals_dict)
    
    # 保存到CSV
    df = save_results_to_csv(results, 'model_rmse_comparison.csv')
    
    # 打印汇总表格
    print("\n=== RMSE结果汇总 ===")
    print(df.to_string(index=False))
    
    # 按数据集分组显示
    print("\n=== 按数据集分组的平均RMSE ===")
    for data_type in data_types:
        if data_type in results:
            time_interval = time_intervals_dict[data_type]
            print(f"\n{data_type} (间隔{time_interval}分钟):")
            for model_type in model_types:
                if model_type in results[data_type]:
                    rmse_30 = results[data_type][model_type]['30min_rmse']
                    rmse_60 = results[data_type][model_type]['60min_rmse']
                    print(f"  {model_type}: 30min={rmse_30:.3f}, 60min={rmse_60:.3f}")