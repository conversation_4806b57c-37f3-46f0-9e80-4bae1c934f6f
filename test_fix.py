#!/usr/bin/env python3
"""
Test the fixed classic_model.py with a single patient
"""
from classic_model import onestep_train

# Test with a single patient from Simulated_adolescent
try:
    print("Testing fixed classic_model with Simulated_adolescent...")
    onestep_train('LR', 'Simulated_adolescent', 'adolescent#001',
                  time_interval=3, time_step=[10, 20], suffix='csv', diabetes_type=1)
    print("✅ Test completed successfully!")
except Exception as e:
    print(f"❌ Test failed with error: {e}")
    import traceback
    traceback.print_exc()
