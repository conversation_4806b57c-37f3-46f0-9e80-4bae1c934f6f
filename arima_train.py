for patient in os.listdir(dataset):
    print(patient)
    patient_name, suffix = patient.split('.')
    data = load_data(dataset + f'/{patient}', suffix)
    
    if suffix in ['xlsx', 'xls']:
        # Shanghai数据集使用这个列名
        if 'CGM (mg / dl)' in data.columns:
            data = data['CGM (mg / dl)'].dropna()
        elif 'CGM' in data.columns:
            data = data['CGM'].dropna()
        else:
            print(f"No CGM column found in {patient}")
            continue
    else:
        # Simulator数据集
        if 'CGM' in data.columns:
            data = data['CGM'].dropna()
        else:
            print(f"No CGM column found in {patient}")
            continue
    
    arima_train(data_type, patient_name, time_interval, data, time_step=[2,4])