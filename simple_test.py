import os

# Test the file paths
data_type = 'ohio_data'
dataset = f'data/{data_type}'

print("Files in ohio_data directory:")
files = os.listdir(dataset)
for file in files[:10]:  # Show first 10 files
    print(f"  {file}")

print("\nExtracting patient IDs:")
patient_ids = set()
for file in files:
    if file.endswith('-ws-training.csv'):
        patient_id = file.replace('-ws-training.csv', '')
        patient_ids.add(patient_id)

print(f"Patient IDs found: {sorted(list(patient_ids))}")

# Test path construction for first patient
if patient_ids:
    first_patient = sorted(list(patient_ids))[0]
    train_path = f'data/{data_type}/{first_patient}-ws-training.csv'
    test_path = f'data/{data_type}/{first_patient}-ws-testing.csv'
    
    print(f"\nTesting paths for patient {first_patient}:")
    print(f"Training path: {train_path}")
    print(f"Training file exists: {os.path.exists(train_path)}")
    print(f"Testing path: {test_path}")
    print(f"Testing file exists: {os.path.exists(test_path)}")
