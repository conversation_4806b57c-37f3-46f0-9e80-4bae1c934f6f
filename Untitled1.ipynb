{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6aba20a0-eb17-449e-8b9b-85d5b13177be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 驱动器 E 中的卷是 flexible\n", " 卷的序列号是 3E51-B085\n", "\n", " E:\\study\\实习\\benchmark 的目录\n", "\n", "2025/07/19  23:35    <DIR>          .\n", "2025/07/19  17:33    <DIR>          ..\n", "2023/05/08  19:06             8,196 .DS_Store\n", "2025/07/19  18:27    <DIR>          .idea\n", "2025/07/19  23:35    <DIR>          .ipynb_checkpoints\n", "2023/05/08  19:06             6,647 ariam_train.py\n", "2023/05/08  19:06            14,257 classic_model.py\n", "2025/07/19  21:26    <DIR>          data\n", "2023/05/08  19:06            50,461 DeepModels.py\n", "2023/05/08  10:58    <DIR>          models\n", "2025/07/19  23:33    <DIR>          predictability\n", "2023/05/08  10:58                25 README.md\n", "2023/05/08  10:58             2,290 Untitled.ipynb\n", "2025/07/19  23:35               337 Untitled1.ipynb\n", "2023/05/08  10:58    <DIR>          util\n", "               7 个文件         82,213 字节\n", "               8 个目录 387,638,157,312 可用字节\n"]}], "source": ["!dir"]}, {"cell_type": "code", "execution_count": 2, "id": "bbb40231-9f4e-48e6-8698-7b7bb5e641a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["卷 flexible 的文件夹 PATH 列表\n", "卷序列号为 3E51-B085\n", "E:.\n", "├─.idea\n", "│  └─inspectionProfiles\n", "├─.ipynb_checkpoints\n", "├─data\n", "│  ├─ohio_data\n", "│  ├─Shanghai_T1DM\n", "│  ├─Shanghai_T2DM\n", "│  ├─Simulated_adolescent\n", "│  ├─Simulator_adult\n", "│  └─Simulator_child\n", "├─models\n", "│  ├─.ipynb_checkpoints\n", "│  └─__pycache__\n", "├─predictability\n", "│  ├─.ipynb_checkpoints\n", "│  └─result\n", "│      └─.ipynb_checkpoints\n", "└─util\n", "    ├─.ipynb_checkpoints\n", "    └─__pycache__\n"]}], "source": ["!tree"]}, {"cell_type": "code", "execution_count": null, "id": "43a6086d-9ece-4712-9015-ba9cd21a2b66", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "shixi(mnist py3.11)", "language": "python", "name": "mnist"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}