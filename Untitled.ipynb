{"cells": [{"cell_type": "code", "execution_count": 5, "id": "50a8c925-ddaf-4c1c-b3b6-c11cb6d07d73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([0.04278805, 0.24793223]),\n", " array([0.00019015, 0.00112232]),\n", " array([0.01658691, 0.09871134]),\n", " array([0.01902465, 0.1125477 ]),\n", " array([0.00951232, 0.05627385]),\n", " array([1., 1.]),\n", " array([1., 1.]),\n", " array([[1., 0., 0., 0., 0.],\n", "       [1., 0., 0., 0., 0.]]),\n", " array([[1., 0., 0., 0., 0.],\n", "       [1., 0., 0., 0., 0.]]),\n", " array([[254.80488566, 248.33585278],\n", "       [251.61437014, 244.98618297],\n", "       [248.33585278, 241.58077894],\n", "       ...,\n", "       [ 38.60026702,  37.26518145],\n", "       [ 37.91822508,  36.64021345],\n", "       [ 37.26518145,  36.04243593]]),\n", " array([[254.80903703, 248.35651769],\n", "       [251.61794684, 245.00400761],\n", "       [248.33885925, 241.59579834],\n", "       ...,\n", "       [ 38.61567655,  37.36445646],\n", "       [ 37.93369723,  36.73979822],\n", "       [ 37.28071214,  36.14230622]])]\n"]}], "source": ["import joblib\n", "import pprint\n", "\n", "file=open(\"Ridge.pkl\",\"rb\")\n", "data=joblib.load(file)\n", "pprint.pprint(data)\n", "file.close()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9ec21600-f879-4b4c-8aca-87ec2d96e080", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "359f6a52-c90a-4ff6-bfbc-251d52aea2a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}