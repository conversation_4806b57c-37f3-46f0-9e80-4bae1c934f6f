#!/usr/bin/env python3
"""
Test script to verify the simulator dataset fix
"""
import os
import pandas as pd
import numpy as np
from util.load_data import load_data
from models.ClassicalModels import gen_data_1d

def test_simulator_data_loading():
    """Test if simulator data can be loaded correctly"""
    data_types = ['Simulated_adolescent', 'Simulator_adult', 'Simulator_child']
    
    for data_type in data_types:
        print(f"\n=== Testing {data_type} ===")
        dataset_path = f'data/{data_type}'
        
        if not os.path.exists(dataset_path):
            print(f"Dataset directory {dataset_path} does not exist!")
            continue
            
        files = os.listdir(dataset_path)
        if not files:
            print(f"No files found in {dataset_path}")
            continue
            
        # Test first file
        test_file = files[0]
        print(f"Testing file: {test_file}")
        
        patient, suffix = test_file.split('.')
        file_path = f'{dataset_path}/{test_file}'
        
        try:
            # Load raw data
            raw_data = load_data(file_path, suffix)
            print(f"Raw data shape: {raw_data.shape}")
            print(f"Columns: {list(raw_data.columns)}")
            
            # Extract CGM data
            if 'CGM' in raw_data.columns:
                cgm_data = raw_data['CGM'].dropna()
                print(f"CGM data length: {len(cgm_data)}")
                print(f"CGM data range: {cgm_data.min():.2f} - {cgm_data.max():.2f}")
                
                # Test gen_data_1d
                history = 30  # 150 minutes / 5 minutes
                horizon = 24  # for 5-minute intervals
                
                if len(cgm_data) >= history + horizon:
                    X, Y = gen_data_1d(cgm_data, history=history, horizon=horizon)
                    print(f"Generated X shape: {X.shape}, Y shape: {Y.shape}")
                    
                    if X.shape[0] > 0:
                        print("✅ Data generation successful!")
                    else:
                        print("❌ No samples generated")
                else:
                    print(f"❌ Insufficient data: need {history + horizon}, got {len(cgm_data)}")
            else:
                print("❌ CGM column not found")
                
        except Exception as e:
            print(f"❌ Error loading data: {e}")

if __name__ == "__main__":
    test_simulator_data_loading()
