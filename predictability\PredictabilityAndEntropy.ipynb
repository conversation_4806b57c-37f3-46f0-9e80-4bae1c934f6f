{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [], "source": ["from Predictability import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["0.7428913814"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["get_predictability([10,20,10,20,10,20,10,20,10,20])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Entropy "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["(0.8941321735745001, 0.30092484657139584, 1.9188209108283718)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Real_Entropy([1]*5), Real_Entropy([1]*50), Real_Entropy(list(np.random.randint(0, 10, 10)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Predictability"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["print(1)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"pycharm": {"is_executing": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["检查可用数据集...\n", "可用数据集: ['ohio_data', 'Shanghai_T1DM', 'Shanghai_T2DM', 'Simulated_adolescent', 'Simulator_adult', 'Simulator_child']\n", "预计处理 167 个文件\n", "每个文件大约需要 2-10 秒（取决于数据大小）\n", "预计总时间: 8 - 22 分钟\n", "==================================================\n", "\n", "开始处理数据集: Shanghai_T2DM\n", "Shanghai_T2DM数据集包含 109 个文件\n"]}, {"name": "stderr", "output_type": "stream", "text": ["处理Shanghai_T2DM: 100%|██████████| 109/109 [00:08<00:00, 13.23it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Shanghai_T2DM 处理完成!\n", "处理了 109 个患者\n", "平均可预测性: 0.7038\n", "耗时: 8.24 秒\n", "--------------------------------------------------\n", "\n", "开始处理数据集: Simulated_adolescent\n", "Simulated_adolescent数据集包含 3 个目标文件（需处理的患者）\n"]}, {"name": "stderr", "output_type": "stream", "text": ["处理Simulated_adolescent: 100%|██████████| 3/3 [00:21<00:00,  7.29s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Simulated_adolescent 处理完成!\n", "处理了 3 个患者\n", "平均可预测性: 0.8648\n", "耗时: 21.89 秒\n", "--------------------------------------------------\n", "\n", "开始处理数据集: Simulator_adult\n", "Simulator_adult数据集包含 4 个目标文件（需处理的患者）\n"]}, {"name": "stderr", "output_type": "stream", "text": ["处理Simulator_adult: 100%|██████████| 4/4 [02:59<00:00, 44.81s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Simulator_adult 处理完成!\n", "处理了 4 个患者\n", "平均可预测性: 0.8309\n", "耗时: 179.25 秒\n", "--------------------------------------------------\n", "\n", "开始处理数据集: Simulator_child\n", "Simulator_child数据集包含 6 个目标文件（需处理的患者）\n"]}, {"name": "stderr", "output_type": "stream", "text": ["处理Simulator_child: 100%|██████████| 6/6 [04:34<00:00, 45.82s/it] "]}, {"name": "stdout", "output_type": "stream", "text": ["Simulator_child 处理完成!\n", "处理了 6 个患者\n", "平均可预测性: 0.9324\n", "耗时: 274.91 秒\n", "--------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from Predictability import *\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import sys\n", "sys.path.append(r'e:\\study\\实习\\benchmark')  # 添加项目根目录到路径\n", "from util.load_data import *\n", "data_path = r'e:\\study\\实习\\benchmark'\n", "# data_type='ohio_data'\n", "data_type='simulator_data'\n", "# data_type='hospital_data'\n", "# data_type='EastT1DM'\n", "\n", "from tqdm import tqdm\n", "import time\n", "\n", "def get_pre(data_type, is_int=True):\n", "    print(f\"\\n开始处理数据集: {data_type}\")\n", "    start_time = time.time()\n", "    \n", "    df = pd.DataFrame(columns=('patient','predictability'))\n", "    mean_predictability = []\n", "    dataset = os.path.join(data_path, 'data', data_type)\n", "    \n", "    # 检查数据集路径是否存在\n", "    if not os.path.exists(dataset):\n", "        print(f\"数据集路径不存在: {dataset}\")\n", "        return 0.0\n", "    \n", "    if data_type == 'ohio_data':\n", "        patient_list = [540, 544, 552, 567, 584, 596, 559, 563, 570, 575, 588, 591]\n", "        print(f\"Ohio数据集包含 {len(patient_list)} 个患者\")\n", "        \n", "        for patient in tqdm(patient_list, desc=f\"处理{data_type}\"):\n", "            train_path = os.path.join(dataset, f'{patient}-ws-training.csv')\n", "            test_path = os.path.join(dataset, f'{patient}-ws-testing.csv')\n", "            \n", "            if not os.path.exists(train_path) or not os.path.exists(test_path):\n", "                print(f\"文件不存在: {patient}\")\n", "                continue\n", "                \n", "            train_data = load_data(train_path, 'csv')['gl_value'].reset_index(drop=True)\n", "            test_data = load_data(test_path, 'csv')['gl_value'].reset_index(drop=True)\n", "            train_data = train_data.dropna(axis=0)\n", "            test_data = test_data.dropna(axis=0)\n", "            data = pd.concat([train_data, test_data])\n", "            \n", "            if is_int:\n", "                data = data.astype(int)\n", "            pre = get_predictability(data)\n", "            series = pd.Series({'patient': patient, 'predictability': pre})\n", "            df = pd.concat([df, series.to_frame().T], ignore_index=True)\n", "            mean_predictability.append(pre)\n", "            \n", "    elif data_type in ['Shanghai_T1DM', 'Shanghai_T2DM']:\n", "        patient_files = [f for f in os.listdir(dataset) if '.' in f]\n", "        print(f\"{data_type}数据集包含 {len(patient_files)} 个文件\")\n", "        \n", "        for patient in tqdm(patient_files, desc=f\"处理{data_type}\"):\n", "            try:\n", "                patient_name, suffix = patient.split('.')\n", "                file_path = os.path.join(dataset, patient)\n", "                \n", "                # 根据文件类型加载数据\n", "                if suffix in ['xlsx', 'xls']:\n", "                    data = pd.read_excel(file_path)\n", "                else:\n", "                    data = load_data(file_path, suffix)\n", "                \n", "                # 查找CGM列\n", "                if 'CGM (mg / dl)' in data.columns:\n", "                    data = data['CGM (mg / dl)'].dropna()\n", "                elif 'CGM' in data.columns:\n", "                    data = data['CGM'].dropna()\n", "                else:\n", "                    print(f\"No CGM column found in {patient}, columns: {data.columns.tolist()}\")\n", "                    continue\n", "                    \n", "                if len(data) == 0:\n", "                    print(f\"No valid data in {patient}\")\n", "                    continue\n", "                    \n", "                if is_int:\n", "                    data = data.astype(int)\n", "                pre = get_predictability(data)\n", "                mean_predictability.append(pre)\n", "                series = pd.Series({'patient': patient_name, 'predictability': pre})\n", "                df = pd.concat([df, series.to_frame().T], ignore_index=True)\n", "            except Exception as e:\n", "                print(f\"处理文件 {patient} 时出错: {e}\")\n", "                continue\n", "                \n", "    else:\n", "        # # 处理模拟器数据集（可能有死亡情况）\n", "        # patient_files = [f for f in os.listdir(dataset) if '.' in f]\n", "        # print(f\"{data_type}数据集包含 {len(patient_files)} 个文件\")\n", "        # 处理模拟器数据集（仅计算指定的患者）\n", "        target_patients = {\n", "            'Simulated_adolescent': ['002', '007', '008'],\n", "            'Simulator_adult': ['003', '005', '008', '009'],\n", "            'Simulator_child': ['001', '002', '003', '006', '007', '008']\n", "        }\n", "        \n", "        # 获取当前数据集的目标患者\n", "        current_targets = target_patients.get(data_type, [])\n", "        if not current_targets:\n", "            print(f\"未找到 {data_type} 的目标患者列表，跳过处理\")\n", "            return 0.0\n", "            \n", "        patient_files = [f for f in os.listdir(dataset) if '.' in f and any(f'#{pid}' in f for pid in current_targets)]\n", "        print(f\"{data_type}数据集包含 {len(patient_files)} 个目标文件（需处理的患者）\")\n", "        for patient in tqdm(patient_files, desc=f\"处理{data_type}\"):\n", "            try:\n", "                patient_name, suffix = patient.split('.')\n", "                file_path = os.path.join(dataset, patient)\n", "                data = load_data(file_path, suffix)        \n", "                # 处理模拟器数据中的死亡情况\n", "                if 'BG' in data.columns:\n", "                    # 找到最后一个 BG >= 0 的索引\n", "                    valid_data = data[data['BG'] >= 0]\n", "                    if len(valid_data) > 0:\n", "                        last_valid_idx = valid_data.index[-1]\n", "                        data = data.loc[:last_valid_idx]\n", "                    else:\n", "                        print(f\"{patient} 的所有 BG 数据均为负值（已死亡），跳过\")\n", "                        continue\n", "                \n", "                # 获取CGM数据\n", "                if 'CGM' in data.columns:\n", "                    data = data['CGM'].dropna()\n", "                else:\n", "                    data = data.iloc[:,0].dropna()\n", "                    \n", "                if len(data) == 0:\n", "                    print(f\"No valid data in {patient}\")\n", "                    continue\n", "                    \n", "                if is_int:\n", "                    data = data.astype(int)\n", "                pre = get_predictability(data)\n", "                mean_predictability.append(pre)\n", "                series = pd.Series({'patient': patient_name, 'predictability': pre})\n", "                df = pd.concat([df, series.to_frame().T], ignore_index=True)\n", "            except Exception as e:\n", "                print(f\"处理文件 {patient} 时出错: {e}\")\n", "                continue\n", "    \n", "    if len(mean_predictability) == 0:\n", "        print(f\"没有成功处理任何数据文件\")\n", "        return 0.0\n", "    \n", "    # 保存结果\n", "    save_dir = f'{data_path}/predictability/result'\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    save_path = f'{save_dir}/{data_type}_int.csv' if is_int else f'{save_dir}/{data_type}.csv'\n", "    df.to_csv(save_path, encoding='utf-8-sig')\n", "    \n", "    elapsed_time = time.time() - start_time\n", "    result = np.mean(mean_predictability)\n", "    print(f\"{data_type} 处理完成!\")\n", "    print(f\"处理了 {len(mean_predictability)} 个患者\")\n", "    print(f\"平均可预测性: {result:.4f}\")\n", "    print(f\"耗时: {elapsed_time:.2f} 秒\")\n", "    print(\"-\" * 50)\n", "    \n", "    return result\n", "\n", "# 估算总时间\n", "print(\"检查可用数据集...\")\n", "data_dir = os.path.join(data_path, 'data')\n", "if os.path.exists(data_dir):\n", "    available_datasets = [item for item in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, item))]\n", "    print(f\"可用数据集: {available_datasets}\")\n", "    \n", "    # 估算文件数量\n", "    total_files = 0\n", "    for dataset in available_datasets:\n", "        dataset_path = os.path.join(data_dir, dataset)\n", "        if dataset == 'ohio_data':\n", "            total_files += 12  # 12个患者\n", "        else:\n", "            files = [f for f in os.listdir(dataset_path) if '.' in f]\n", "            total_files += len(files)\n", "    \n", "    print(f\"预计处理 {total_files} 个文件\")\n", "    print(f\"每个文件大约需要 2-10 秒（取决于数据大小）\")\n", "    print(f\"预计总时间: {total_files * 3 // 60} - {total_files * 8 // 60} 分钟\")\n", "    print(\"=\" * 50)\n", "else:\n", "    print(f\"数据目录不存在: {data_dir}\")\n", "    available_datasets = []\n", "\n", "# 处理数据集\n", "# 'ohio_data', 'simulator_data', 'Shanghai_T1DM', 'Shanghai_T2DM', \n", "for dataset in ['Shanghai_T2DM', 'Simulated_adolescent', 'Simulator_adult', 'Simulator_child']:\n", "    if dataset in available_datasets:\n", "        res = get_pre(dataset, is_int=True)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import joblib\n", "plt.rcParams['font.family'] = ['sans-serif']\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "\n", "# data_type='ohio_data'\n", "data_type='simulator_data'\n", "# data_type='hospital_data'\n", "metric='rmse'\n", "time_hotizon=30\n", "# def get_predict_metric(data_type='simulator_data',metric='rmse',time_hotizon=60,time_interval=5):\n", "#     获取one_data的GRU结果\n", "#     source_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\predictability\\result'\n", "#     data_path=f'{source_path}/{data_type}_int.csv'\n", "#     data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )\n", "#     result_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\result\\multi_step'\n", "#     result_data_path=f'{result_path}/{data_type}_epoch_1000'\n", "#     gru_data=None\n", "#     for patient in os.listdir(result_data_path):\n", "#         gru_path=f'{result_data_path}/{patient}/GRU_1.csv'\n", "#         gru=pd.read_csv(gru_path)\n", "#         gru['patient']=patient\n", "#         gru['patient']=gru['patient'].astype(object)\n", "#         gru.rename(columns={'Unnamed: 0':\"metric\"},inplace=True)\n", "#         gru=gru[gru['metric']==metric]\n", "#         if gru_data is None:\n", "#             gru_data=gru\n", "#         else:\n", "#             gru_data=gru_data.append(gru)\n", "#     data=pd.merge(data,gru_data,how='left',on='patient')\n", "#     # print(data)\n", "#     data=data.sort_values('predictability',ascending=True).reset_index(drop=True)\n", "#     \n", "#     fig=plt.figure(figsize=(8,6))\n", "#     ax1=fig.add_subplot(111)\n", "#     data['predictability'].plot(ax=ax1,style='b',label='predictability')\n", "#     ax1.set_ylabel(\"可预测性\")\n", "#     # ax1.set_yticks(np.arange(0.8,0.9,0.01))\n", "#     plt.legend(loc=2)\n", "#     plt.xlabel('根据可预测性排序的模拟器患者')\n", "#     \n", "#     ax2=ax1.twinx()\n", "#     data[str(time_hotizon//time_interval-1)].plot(ax=ax2,style='r',label='30 minutes')\n", "#     # ax2.set_yticks(np.arange(13,35,2))\n", "#     ax2.set_ylabel(\"RMSE\")\n", "#     plt.legend(loc=1)\n", "#     \n", "#     # plt.grid()\n", "#     plt.title(f'{time_hotizon}分钟的{metric}比较')\n", "#     plt.savefig(f'figure/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')\n", "    \n", "# def get_predict_metric(data_type='simulator_data',metric='rmse',time_hotizon=60,time_interval=5):\n", "#     # 获取all_data的GRU结果\n", "#     source_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\predictability\\result'\n", "#     data_path=f'{source_path}/{data_type}_int.csv'\n", "#     data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )\n", "#     result_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\result\\multi_step_all_data'\n", "#     result_data_path=f'{result_path}/{data_type}_epoch_300'\n", "#     gru_data=None\n", "#     for patient in os.listdir(result_data_path):\n", "#         gru_path=f'{result_data_path}/{patient}/GRU.csv'\n", "#         gru=pd.read_csv(gru_path)\n", "#         gru['patient']=patient\n", "#         gru['patient']=gru['patient'].astype(object)\n", "#         gru.rename(columns={'Unnamed: 0':\"metric\"},inplace=True)\n", "#         gru=gru[gru['metric']==metric]\n", "#         if gru_data is None:\n", "#             gru_data=gru\n", "#         else:\n", "#             gru_data=gru_data.append(gru)\n", "#     data=pd.merge(data,gru_data,how='left',on='patient')\n", "#     # print(data)\n", "#     data=data.sort_values('predictability',ascending=True).reset_index(drop=True)\n", "#     \n", "#     fig=plt.figure(figsize=(8,6))\n", "#     ax1=fig.add_subplot(111)\n", "#     data['predictability'].plot(ax=ax1,style='b',label='predictability')\n", "#     ax1.set_ylabel(\"可预测性\")\n", "#     # ax1.set_yticks(np.arange(0.8,0.9,0.01))\n", "#     plt.legend(loc=2)\n", "#     plt.xlabel('根据可预测性排序的模拟器患者')\n", "#     \n", "#     ax2=ax1.twinx()\n", "#     data[str(time_hotizon//time_interval-1)].plot(ax=ax2,style='r',label='30 minutes')\n", "#     # ax2.set_yticks(np.arange(13,35,2))\n", "#     ax2.set_ylabel(metric)\n", "#     plt.legend(loc=1)\n", "#     \n", "#     # plt.grid()\n", "#     plt.title(f'{time_hotizon}分钟的{metric}比较')\n", "#     plt.savefig(f'figure/all_data/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')\n", "\n", "def get_predict_metric(data_type='simulator_data', metric='rmse', time_hotizon=60, time_interval=5, model_type='GRU'):\n", "    source_path = f'{data_path}/predictability/result'  # 修复：使用正确路径\n", "    data_path_csv = f'{source_path}/{data_type}_int.csv'\n", "    data = pd.read_csv(data_path_csv, index_col=0, converters={'patient': str})\n", "    result_path = f'{data_path}/result/one_step'  # 修复：使用正确路径\n", "    result_data_path = f'{result_path}/{data_type}_epoch_1000'\n", "    \n", "    gru_data = None\n", "    for patient in os.listdir(result_data_path):\n", "        gru_path = f'{result_data_path}/{patient}/{model_type}.pkl'\n", "        gru = joblib.load(gru_path)[f'{time_hotizon}minuts']\n", "        gru = pd.DataFrame({'patient': [patient], metric: [gru[4]]})\n", "        if gru_data is None:\n", "            gru_data = gru\n", "        else:\n", "            gru_data = pd.concat([gru_data, gru], ignore_index=True)  # 修复：使用concat\n", "    \n", "    data = pd.merge(data, gru_data, how='left', on='patient')\n", "    data = data.sort_values('predictability', ascending=True).reset_index(drop=True)\n", "    data['predictability'] = 1 - data['predictability']\n", "    print(data)\n", "    \n", "    fig = plt.figure(figsize=(8,6))\n", "    ax1 = fig.add_subplot(111)\n", "    data['predictability'].plot(ax=ax1, style='b', label='predictability')\n", "    ax1.set_ylabel(\"可预测性\")\n", "    plt.legend(loc=2)\n", "    plt.xlabel('根据可预测性排序的患者')\n", "    \n", "    ax2 = ax1.twinx()\n", "    data[metric].plot(ax=ax2, style='r', label=f'{time_hotizon} minutes')\n", "    ax2.set_ylabel(metric)\n", "    plt.legend(loc=1)\n", "    \n", "    plt.title(f'{time_hotizon}分钟的{metric}比较-{model_type}')\n", "    plt.savefig(f'{data_path}/predictability/figure/one_data/{model_type}/{data_type}_{metric}_{time_hotizon}.png', dpi=400, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [], "source": ["def get_multi_model(data_type='simulator_data',metric='rmse',time_hotizon=30,time_interval=5):\n", "    source_path=r'e:\\study\\实习\\benchmark\\predictability\\result'\n", "    data_path=f'{source_path}/{data_type}_int.csv'\n", "    data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )\n", "    data['predictability']=1-data['predictability']\n", "    data=data.sort_values('predictability',ascending=False).reset_index(drop=True)\n", "\n", "    result_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\result\\one_step'\n", "    result_data_path=f'{result_path}/{data_type}_30'\n", "    ridge_data=None\n", "    svr_data=None\n", "    RF_data=None\n", "    gru_data=None\n", "    for patient in os.listdir(result_data_path):\n", "        gru_path=f'{result_data_path}/{patient}/Ridge.pkl'\n", "        gru=joblib.load(gru_path)\n", "        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})\n", "        if ridge_data is None:\n", "            ridge_data=gru\n", "        else:\n", "            ridge_data=ridge_data.append(gru)\n", "    for patient in os.listdir(result_data_path):\n", "        gru_path=f'{result_data_path}/{patient}/SVR.pkl'\n", "        gru=joblib.load(gru_path)\n", "        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})\n", "        if svr_data is None:\n", "            svr_data=gru\n", "        else:\n", "            svr_data=svr_data.append(gru)  \n", "    for patient in os.listdir(result_data_path):\n", "        gru_path=f'{result_data_path}/{patient}/RF.pkl'\n", "        gru=joblib.load(gru_path)\n", "        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})\n", "        if RF_data is None:\n", "            RF_data=gru\n", "        else:\n", "            RF_data=RF_data.append(gru)\n", "            \n", "    result_path=r'D:\\MyWorks\\糖尿病预测\\blood_glucose_prediction\\result\\multi_step'\n", "    result_data_path=f'{result_path}/{data_type}_epoch_1000'\n", "    for patient in os.listdir(result_data_path):\n", "        gru_path=f'{result_data_path}/{patient}/GRU.pkl'\n", "        gru=joblib.load(gru_path)\n", "        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})\n", "        if gru_data is None:\n", "            gru_data=gru\n", "        else:\n", "            gru_data=gru_data.append(gru)\n", "            \n", "    merge_gru=pd.merge(data,gru_data,how='left',on='patient')\n", "    merge_ridge=pd.merge(data,ridge_data,how='left',on='patient')\n", "    merge_svr=pd.merge(data,svr_data,how='left',on='patient')\n", "    merge_rf=pd.merge(data,RF_data,how='left',on='patient')\n", "    print(merge_gru)\n", "    print(merge_ridge)\n", "    fig=plt.figure(figsize=(8,6))\n", "    ax1=fig.add_subplot(111)\n", "    data['predictability'].plot(ax=ax1,style='r--',label='predictability',lw=3)\n", "    ax1.set_ylabel(\"可预测性\")\n", "    # ax1.set_yticks(np.arange(0.8,0.9,0.01))\n", "    plt.legend(loc=2)\n", "    plt.xlabel('根据可预测性排序的模拟器患者')\n", "    \n", "    # ax2=ax1.twinx()\n", "    merge_ridge['smape'].plot(ax=ax1,style='b',label='ridge',lw=3)\n", "    merge_gru['smape'].plot(ax=ax1,label='gru',lw=3)\n", "    merge_svr['smape'].plot(ax=ax1,label='SVR',lw=3)\n", "    merge_rf['smape'].plot(ax=ax1,label='RF',lw=3)\n", "    # ax2.set_yticks(np.arange(13,35,2))\n", "    # ax2.set_ylabel(metric)\n", "    plt.legend(loc=1)\n", "    \n", "    plt.grid()\n", "    plt.title(f'{time_hotizon}分钟的{metric}比较-{data_type}')\n", "    plt.savefig(f'figure/compare_figure/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 247, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                             patient  predictability     smape\n", "0        child#004_simulation_8weeks        0.175650  5.279063\n", "1        child#005_simulation_8weeks        0.167038  4.927157\n", "2        child#001_simulation_8weeks        0.159518  8.217657\n", "3        adult#003_simulation_8weeks        0.156791  6.579209\n", "4        child#002_simulation_8weeks        0.149233  4.861039\n", "5        adult#006_simulation_8weeks        0.146556  5.010334\n", "6   adolescent#002_simulation_8weeks        0.144547  7.342054\n", "7   adolescent#003_simulation_8weeks        0.144062  4.986194\n", "8   adolescent#006_simulation_8weeks        0.143994  6.095695\n", "9        adult#004_simulation_8weeks        0.140785  5.268201\n", "10       child#010_simulation_8weeks        0.139963  6.704653\n", "11  adolescent#004_simulation_8weeks        0.138492  5.451986\n", "12  adolescent#005_simulation_8weeks        0.136931  5.120737\n", "13  adolescent#008_simulation_8weeks        0.135907  6.685163\n", "14       child#003_simulation_8weeks        0.131883  6.844024\n", "15       adult#009_simulation_8weeks        0.130977  5.409743\n", "16       adult#005_simulation_8weeks        0.130773  5.226716\n", "17  adolescent#010_simulation_8weeks        0.130548  7.629391\n", "18       child#007_simulation_8weeks        0.130446  5.777715\n", "19       adult#008_simulation_8weeks        0.129153  4.633910\n", "20       adult#002_simulation_8weeks        0.126433  4.795423\n", "21  adolescent#001_simulation_8weeks        0.125905  3.980267\n", "22  adolescent#009_simulation_8weeks        0.125291  4.909892\n", "23       child#009_simulation_8weeks        0.124442  5.303547\n", "24       adult#007_simulation_8weeks        0.124293  4.948204\n", "25  adolescent#007_simulation_8weeks        0.122701  6.534150\n", "26       adult#001_simulation_8weeks        0.122564  5.269122\n", "27       child#008_simulation_8weeks        0.122135  8.392189\n", "28       adult#010_simulation_8weeks        0.121018  5.360938\n", "29       child#006_simulation_8weeks        0.120584  6.761459\n", "  patient  predictability     smape\n", "0     584        0.285522  5.050655\n", "1     540        0.285002  6.223007\n", "2     567        0.284194  4.944866\n", "3     544        0.284163  3.935468\n", "4     552        0.281217  2.961185\n", "5     596        0.272293  4.216856\n", "   patient  predictability      smape\n", "0      孙正国        0.340740   8.844707\n", "1      胡金秋        0.322304   7.664118\n", "2      孔建平        0.319923   8.968760\n", "3      黄连辉        0.316163   5.671542\n", "4      王桂芳        0.313254   6.357770\n", "5      王黛萍        0.312715   8.425420\n", "6      朱云仙        0.299626   5.026082\n", "7      乔忠明        0.296379   9.400112\n", "8      刘荣娣        0.291174   5.166033\n", "9      吴燕平        0.290931  13.604232\n", "10     杨俊数        0.288584   5.124129\n", "11     祝高荣        0.286708   5.882921\n", "12     孙振阳        0.286547  10.820733\n", "13     夏精明        0.284568   4.796185\n", "14     蔡俐数        0.282586   5.823112\n", "15     何翠菊        0.282375   3.781152\n", "16     蒋玉燕        0.281366   6.197535\n", "17     夏震琴        0.280045   6.180781\n", "18     钱茹德        0.267912   3.694978\n", "19     侯剑数        0.265622   3.530622\n", "20     顾乃强        0.261998   4.641754\n", "21     孔令花        0.258626   4.066552\n", "22     周伽一        0.245305   4.031723\n"]}], "source": ["metric='smape'\n", "# get_predict_metric('simulator_data',metric,5)\n", "get_predict_metric('simulator_data',metric,30,model_type='GRU')\n", "# get_predict_metric('simulator_data',metric,60)\n", "# get_predict_metric('ohio_data',metric,5)\n", "get_predict_metric('ohio_data',metric,30,model_type='GRU')\n", "# get_predict_metric('ohio_data',metric,60)\n", "# get_predict_metric('hospital_data',metric,15,15)\n", "get_predict_metric('hospital_data',metric,30,15,model_type='GRU')\n", "# get_predict_metric('hospital_data',metric,60,15)"]}, {"cell_type": "code", "execution_count": 279, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                             patient  predictability     smape\n", "0        child#004_simulation_8weeks        0.175650  0.020004\n", "1        child#005_simulation_8weeks        0.167038  0.019624\n", "2        child#001_simulation_8weeks        0.159518  0.038671\n", "3        adult#003_simulation_8weeks        0.156791  0.028449\n", "4        child#002_simulation_8weeks        0.149233  0.017845\n", "5        adult#006_simulation_8weeks        0.146556  0.020043\n", "6   adolescent#002_simulation_8weeks        0.144547  0.031206\n", "7   adolescent#003_simulation_8weeks        0.144062  0.017136\n", "8   adolescent#006_simulation_8weeks        0.143994  0.025756\n", "9        adult#004_simulation_8weeks        0.140785  0.020472\n", "10       child#010_simulation_8weeks        0.139963  0.027306\n", "11  adolescent#004_simulation_8weeks        0.138492  0.023091\n", "12  adolescent#005_simulation_8weeks        0.136931  0.019393\n", "13  adolescent#008_simulation_8weeks        0.135907  0.025697\n", "14       child#003_simulation_8weeks        0.131883  0.025431\n", "15       adult#009_simulation_8weeks        0.130977  0.021288\n", "16       adult#005_simulation_8weeks        0.130773  0.020393\n", "17  adolescent#010_simulation_8weeks        0.130548  0.028275\n", "18       child#007_simulation_8weeks        0.130446  0.020800\n", "19       adult#008_simulation_8weeks        0.129153  0.019053\n", "20       adult#002_simulation_8weeks        0.126433  0.017536\n", "21  adolescent#001_simulation_8weeks        0.125905  0.015141\n", "22  adolescent#009_simulation_8weeks        0.125291  0.019243\n", "23       child#009_simulation_8weeks        0.124442  0.020350\n", "24       adult#007_simulation_8weeks        0.124293  0.015630\n", "25  adolescent#007_simulation_8weeks        0.122701  0.026102\n", "26       adult#001_simulation_8weeks        0.122564  0.024505\n", "27       child#008_simulation_8weeks        0.122135  0.039608\n", "28       adult#010_simulation_8weeks        0.121018  0.024048\n", "29       child#006_simulation_8weeks        0.120584  0.026956\n", "                             patient  predictability     smape\n", "0        child#004_simulation_8weeks        0.175650  0.009403\n", "1        child#005_simulation_8weeks        0.167038  0.004787\n", "2        child#001_simulation_8weeks        0.159518  0.025416\n", "3        adult#003_simulation_8weeks        0.156791  0.010897\n", "4        child#002_simulation_8weeks        0.149233  0.004367\n", "5        adult#006_simulation_8weeks        0.146556  0.005826\n", "6   adolescent#002_simulation_8weeks        0.144547  0.018315\n", "7   adolescent#003_simulation_8weeks        0.144062  0.006528\n", "8   adolescent#006_simulation_8weeks        0.143994  0.010134\n", "9        adult#004_simulation_8weeks        0.140785  0.010079\n", "10       child#010_simulation_8weeks        0.139963  0.015290\n", "11  adolescent#004_simulation_8weeks        0.138492  0.008322\n", "12  adolescent#005_simulation_8weeks        0.136931  0.007879\n", "13  adolescent#008_simulation_8weeks        0.135907  0.010918\n", "14       child#003_simulation_8weeks        0.131883  0.018242\n", "15       adult#009_simulation_8weeks        0.130977  0.006542\n", "16       adult#005_simulation_8weeks        0.130773  0.005910\n", "17  adolescent#010_simulation_8weeks        0.130548  0.009889\n", "18       child#007_simulation_8weeks        0.130446  0.010931\n", "19       adult#008_simulation_8weeks        0.129153  0.004736\n", "20       adult#002_simulation_8weeks        0.126433  0.003913\n", "21  adolescent#001_simulation_8weeks        0.125905  0.001791\n", "22  adolescent#009_simulation_8weeks        0.125291  0.007586\n", "23       child#009_simulation_8weeks        0.124442  0.018664\n", "24       adult#007_simulation_8weeks        0.124293  0.007528\n", "25  adolescent#007_simulation_8weeks        0.122701  0.010661\n", "26       adult#001_simulation_8weeks        0.122564  0.006962\n", "27       child#008_simulation_8weeks        0.122135  0.026977\n", "28       adult#010_simulation_8weeks        0.121018  0.010512\n", "29       child#006_simulation_8weeks        0.120584  0.013649\n", "  patient  predictability     smape\n", "0     584        0.285522  0.026062\n", "1     540        0.285002  0.032159\n", "2     567        0.284194  0.023320\n", "3     544        0.284163  0.025457\n", "4     552        0.281217  0.018174\n", "5     596        0.272293  0.026090\n", "  patient  predictability     smape\n", "0     584        0.285522  0.051266\n", "1     540        0.285002  0.057767\n", "2     567        0.284194  0.055317\n", "3     544        0.284163  0.039409\n", "4     552        0.281217  0.031281\n", "5     596        0.272293  0.045598\n", "   patient  predictability     smape\n", "0      孙正国        0.340740  0.100279\n", "1      胡金秋        0.322304  0.119682\n", "2      孔建平        0.319923  0.121495\n", "3      黄连辉        0.316163  0.092665\n", "4      王桂芳        0.313254  0.152451\n", "5      王黛萍        0.312715  0.094920\n", "6      朱云仙        0.299626  0.069001\n", "7      乔忠明        0.296379  0.113757\n", "8      刘荣娣        0.291174  0.081180\n", "9      吴燕平        0.290931  0.131352\n", "10     杨俊数        0.288584  0.086649\n", "11     祝高荣        0.286708  0.096993\n", "12     孙振阳        0.286547  0.122796\n", "13     夏精明        0.284568  0.074762\n", "14     蔡俐数        0.282586  0.092368\n", "15     何翠菊        0.282375  0.061292\n", "16     蒋玉燕        0.281366  0.107390\n", "17     夏震琴        0.280045  0.063708\n", "18     钱茹德        0.267912  0.046625\n", "19     侯剑数        0.265622  0.080045\n", "20     顾乃强        0.261998  0.063997\n", "21     孔令花        0.258626  0.049489\n", "22     周伽一        0.245305  0.056216\n", "   patient  predictability     smape\n", "0      孙正国        0.340740  0.066148\n", "1      胡金秋        0.322304  0.035803\n", "2      孔建平        0.319923  0.038634\n", "3      黄连辉        0.316163  0.051584\n", "4      王桂芳        0.313254  0.041908\n", "5      王黛萍        0.312715  0.026042\n", "6      朱云仙        0.299626  0.029786\n", "7      乔忠明        0.296379  0.021618\n", "8      刘荣娣        0.291174  0.031688\n", "9      吴燕平        0.290931  0.062948\n", "10     杨俊数        0.288584  0.043842\n", "11     祝高荣        0.286708  0.031624\n", "12     孙振阳        0.286547  0.027304\n", "13     夏精明        0.284568  0.039163\n", "14     蔡俐数        0.282586  0.032219\n", "15     何翠菊        0.282375  0.026881\n", "16     蒋玉燕        0.281366  0.048637\n", "17     夏震琴        0.280045  0.031800\n", "18     钱茹德        0.267912  0.033684\n", "19     侯剑数        0.265622  0.032901\n", "20     顾乃强        0.261998  0.033970\n", "21     孔令花        0.258626  0.035640\n", "22     周伽一        0.245305  0.028133\n"]}], "source": ["# get_multi_model('simulator_data',metric,5)\n", "get_multi_model('simulator_data',metric,30)\n", "# get_multi_model('simulator_data',metric,60)\n", "# get_multi_model('ohio_data',metric,5)\n", "get_multi_model('ohio_data',metric,30)\n", "# get_multi_model('ohio_data',metric,60)\n", "# get_multi_model('hospital_data',metric,15,15)\n", "get_multi_model('hospital_data',metric,30,15)\n", "# get_multi_model('hospital_data',metric,60,15)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EastT1DM_int.csv count    12.000000\n", "mean      0.691882\n", "std       0.018849\n", "min       0.667403\n", "25%       0.677950\n", "50%       0.689370\n", "75%       0.707486\n", "max       0.721103\n", "Name: predictability, dtype: float64\n", "hospital_data_int.csv count    91.000000\n", "mean      0.706269\n", "std       0.028742\n", "min       0.653962\n", "25%       0.685990\n", "50%       0.702497\n", "75%       0.717520\n", "max       0.861208\n", "Name: predictability, dtype: float64\n", "ohio_data.csv count       0\n", "unique      0\n", "top       NaN\n", "freq      <PERSON>\n", "Name: predictability, dtype: object\n", "ohio_data_int.csv count    12.000000\n", "mean      0.889272\n", "std       0.070086\n", "min       0.803418\n", "25%       0.830054\n", "50%       0.870501\n", "75%       0.964096\n", "max       0.986480\n", "Name: predictability, dtype: float64\n", "simulator_data_int.csv count    30.000000\n", "mean      0.863380\n", "std       0.014155\n", "min       0.824350\n", "25%       0.855955\n", "50%       0.868570\n", "75%       0.874556\n", "max       0.879416\n", "Name: predictability, dtype: float64\n"]}], "source": ["import os\n", "import pandas as pd\n", "for d in os.listdir('result'):\n", "    data=pd.read_csv(f'result/{d}',index_col=0)['predictability']\n", "    print(d,data.describe())\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "mnist", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "pycharm": {"stem_cell": {"cell_type": "raw", "metadata": {"collapsed": false}, "source": []}}}, "nbformat": 4, "nbformat_minor": 4}