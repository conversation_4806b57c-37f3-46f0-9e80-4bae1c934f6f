from Predictability import *

get_predictability([10,20,10,20,10,20,10,20,10,20])

Real_Entropy([1]*5), Real_Entropy([1]*50), Real_Entropy(list(np.random.randint(0, 10, 10)))

print(1)

from Predictability import *
import os
import numpy as np
import pandas as pd
import sys
sys.path.append(r'e:\study\实习\benchmark')  # 添加项目根目录到路径
from util.load_data import *
data_path = r'e:\study\实习\benchmark'
# data_type='ohio_data'
data_type='simulator_data'
# data_type='hospital_data'
# data_type='EastT1DM'

from tqdm import tqdm
import time

def get_pre(data_type, is_int=True):
    print(f"\n开始处理数据集: {data_type}")
    start_time = time.time()
    
    df = pd.DataFrame(columns=('patient','predictability'))
    mean_predictability = []
    dataset = os.path.join(data_path, 'data', data_type)
    
    # 检查数据集路径是否存在
    if not os.path.exists(dataset):
        print(f"数据集路径不存在: {dataset}")
        return 0.0
    
    if data_type == 'ohio_data':
        patient_list = [540, 544, 552, 567, 584, 596, 559, 563, 570, 575, 588, 591]
        print(f"Ohio数据集包含 {len(patient_list)} 个患者")
        
        for patient in tqdm(patient_list, desc=f"处理{data_type}"):
            train_path = os.path.join(dataset, f'{patient}-ws-training.csv')
            test_path = os.path.join(dataset, f'{patient}-ws-testing.csv')
            
            if not os.path.exists(train_path) or not os.path.exists(test_path):
                print(f"文件不存在: {patient}")
                continue
                
            train_data = load_data(train_path, 'csv')['gl_value'].reset_index(drop=True)
            test_data = load_data(test_path, 'csv')['gl_value'].reset_index(drop=True)
            train_data = train_data.dropna(axis=0)
            test_data = test_data.dropna(axis=0)
            data = pd.concat([train_data, test_data])
            
            if is_int:
                data = data.astype(int)
            pre = get_predictability(data)
            series = pd.Series({'patient': patient, 'predictability': pre})
            df = pd.concat([df, series.to_frame().T], ignore_index=True)
            mean_predictability.append(pre)
            
    elif data_type in ['Shanghai_T1DM', 'Shanghai_T2DM']:
        patient_files = [f for f in os.listdir(dataset) if '.' in f]
        print(f"{data_type}数据集包含 {len(patient_files)} 个文件")
        
        for patient in tqdm(patient_files, desc=f"处理{data_type}"):
            try:
                patient_name, suffix = patient.split('.')
                file_path = os.path.join(dataset, patient)
                
                # 根据文件类型加载数据
                if suffix in ['xlsx', 'xls']:
                    data = pd.read_excel(file_path)
                else:
                    data = load_data(file_path, suffix)
                
                # 查找CGM列
                if 'CGM (mg / dl)' in data.columns:
                    data = data['CGM (mg / dl)'].dropna()
                elif 'CGM' in data.columns:
                    data = data['CGM'].dropna()
                else:
                    print(f"No CGM column found in {patient}, columns: {data.columns.tolist()}")
                    continue
                    
                if len(data) == 0:
                    print(f"No valid data in {patient}")
                    continue
                    
                if is_int:
                    data = data.astype(int)
                pre = get_predictability(data)
                mean_predictability.append(pre)
                series = pd.Series({'patient': patient_name, 'predictability': pre})
                df = pd.concat([df, series.to_frame().T], ignore_index=True)
            except Exception as e:
                print(f"处理文件 {patient} 时出错: {e}")
                continue
                
    else:
        # # 处理模拟器数据集（可能有死亡情况）
        # patient_files = [f for f in os.listdir(dataset) if '.' in f]
        # print(f"{data_type}数据集包含 {len(patient_files)} 个文件")
        # 处理模拟器数据集（仅计算指定的患者）
        target_patients = {
            'Simulated_adolescent': ['002', '007', '008'],
            'Simulator_adult': ['003', '005', '008', '009'],
            'Simulator_child': ['001', '002', '003', '006', '007', '008']
        }
        
        # 获取当前数据集的目标患者
        current_targets = target_patients.get(data_type, [])
        if not current_targets:
            print(f"未找到 {data_type} 的目标患者列表，跳过处理")
            return 0.0
            
        patient_files = [f for f in os.listdir(dataset) if '.' in f and any(f'#{pid}' in f for pid in current_targets)]
        print(f"{data_type}数据集包含 {len(patient_files)} 个目标文件（需处理的患者）")
        for patient in tqdm(patient_files, desc=f"处理{data_type}"):
            try:
                patient_name, suffix = patient.split('.')
                file_path = os.path.join(dataset, patient)
                data = load_data(file_path, suffix)        
                # 处理模拟器数据中的死亡情况
                if 'BG' in data.columns:
                    # 找到最后一个 BG >= 0 的索引
                    valid_data = data[data['BG'] >= 0]
                    if len(valid_data) > 0:
                        last_valid_idx = valid_data.index[-1]
                        data = data.loc[:last_valid_idx]
                    else:
                        print(f"{patient} 的所有 BG 数据均为负值（已死亡），跳过")
                        continue
                
                # 获取CGM数据
                if 'CGM' in data.columns:
                    data = data['CGM'].dropna()
                else:
                    data = data.iloc[:,0].dropna()
                    
                if len(data) == 0:
                    print(f"No valid data in {patient}")
                    continue
                    
                if is_int:
                    data = data.astype(int)
                pre = get_predictability(data)
                mean_predictability.append(pre)
                series = pd.Series({'patient': patient_name, 'predictability': pre})
                df = pd.concat([df, series.to_frame().T], ignore_index=True)
            except Exception as e:
                print(f"处理文件 {patient} 时出错: {e}")
                continue
    
    if len(mean_predictability) == 0:
        print(f"没有成功处理任何数据文件")
        return 0.0
    
    # 保存结果
    save_dir = f'{data_path}/predictability/result'
    os.makedirs(save_dir, exist_ok=True)
    save_path = f'{save_dir}/{data_type}_int.csv' if is_int else f'{save_dir}/{data_type}.csv'
    df.to_csv(save_path, encoding='utf-8-sig')
    
    elapsed_time = time.time() - start_time
    result = np.mean(mean_predictability)
    print(f"{data_type} 处理完成!")
    print(f"处理了 {len(mean_predictability)} 个患者")
    print(f"平均可预测性: {result:.4f}")
    print(f"耗时: {elapsed_time:.2f} 秒")
    print("-" * 50)
    
    return result

# 估算总时间
print("检查可用数据集...")
data_dir = os.path.join(data_path, 'data')
if os.path.exists(data_dir):
    available_datasets = [item for item in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, item))]
    print(f"可用数据集: {available_datasets}")
    
    # 估算文件数量
    total_files = 0
    for dataset in available_datasets:
        dataset_path = os.path.join(data_dir, dataset)
        if dataset == 'ohio_data':
            total_files += 12  # 12个患者
        else:
            files = [f for f in os.listdir(dataset_path) if '.' in f]
            total_files += len(files)
    
    print(f"预计处理 {total_files} 个文件")
    print(f"每个文件大约需要 2-10 秒（取决于数据大小）")
    print(f"预计总时间: {total_files * 3 // 60} - {total_files * 8 // 60} 分钟")
    print("=" * 50)
else:
    print(f"数据目录不存在: {data_dir}")
    available_datasets = []

# 处理数据集
# 'ohio_data', 'simulator_data', 'Shanghai_T1DM', 'Shanghai_T2DM', 
for dataset in ['Shanghai_T2DM', 'Simulated_adolescent', 'Simulator_adult', 'Simulator_child']:
    if dataset in available_datasets:
        res = get_pre(dataset, is_int=True)

import pandas as pd
import matplotlib.pyplot as plt
import joblib
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['SimHei']

# data_type='ohio_data'
data_type='simulator_data'
# data_type='hospital_data'
metric='rmse'
time_hotizon=30
# def get_predict_metric(data_type='simulator_data',metric='rmse',time_hotizon=60,time_interval=5):
#     获取one_data的GRU结果
#     source_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\predictability\result'
#     data_path=f'{source_path}/{data_type}_int.csv'
#     data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )
#     result_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\result\multi_step'
#     result_data_path=f'{result_path}/{data_type}_epoch_1000'
#     gru_data=None
#     for patient in os.listdir(result_data_path):
#         gru_path=f'{result_data_path}/{patient}/GRU_1.csv'
#         gru=pd.read_csv(gru_path)
#         gru['patient']=patient
#         gru['patient']=gru['patient'].astype(object)
#         gru.rename(columns={'Unnamed: 0':"metric"},inplace=True)
#         gru=gru[gru['metric']==metric]
#         if gru_data is None:
#             gru_data=gru
#         else:
#             gru_data=gru_data.append(gru)
#     data=pd.merge(data,gru_data,how='left',on='patient')
#     # print(data)
#     data=data.sort_values('predictability',ascending=True).reset_index(drop=True)
#     
#     fig=plt.figure(figsize=(8,6))
#     ax1=fig.add_subplot(111)
#     data['predictability'].plot(ax=ax1,style='b',label='predictability')
#     ax1.set_ylabel("可预测性")
#     # ax1.set_yticks(np.arange(0.8,0.9,0.01))
#     plt.legend(loc=2)
#     plt.xlabel('根据可预测性排序的模拟器患者')
#     
#     ax2=ax1.twinx()
#     data[str(time_hotizon//time_interval-1)].plot(ax=ax2,style='r',label='30 minutes')
#     # ax2.set_yticks(np.arange(13,35,2))
#     ax2.set_ylabel("RMSE")
#     plt.legend(loc=1)
#     
#     # plt.grid()
#     plt.title(f'{time_hotizon}分钟的{metric}比较')
#     plt.savefig(f'figure/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')
    
# def get_predict_metric(data_type='simulator_data',metric='rmse',time_hotizon=60,time_interval=5):
#     # 获取all_data的GRU结果
#     source_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\predictability\result'
#     data_path=f'{source_path}/{data_type}_int.csv'
#     data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )
#     result_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\result\multi_step_all_data'
#     result_data_path=f'{result_path}/{data_type}_epoch_300'
#     gru_data=None
#     for patient in os.listdir(result_data_path):
#         gru_path=f'{result_data_path}/{patient}/GRU.csv'
#         gru=pd.read_csv(gru_path)
#         gru['patient']=patient
#         gru['patient']=gru['patient'].astype(object)
#         gru.rename(columns={'Unnamed: 0':"metric"},inplace=True)
#         gru=gru[gru['metric']==metric]
#         if gru_data is None:
#             gru_data=gru
#         else:
#             gru_data=gru_data.append(gru)
#     data=pd.merge(data,gru_data,how='left',on='patient')
#     # print(data)
#     data=data.sort_values('predictability',ascending=True).reset_index(drop=True)
#     
#     fig=plt.figure(figsize=(8,6))
#     ax1=fig.add_subplot(111)
#     data['predictability'].plot(ax=ax1,style='b',label='predictability')
#     ax1.set_ylabel("可预测性")
#     # ax1.set_yticks(np.arange(0.8,0.9,0.01))
#     plt.legend(loc=2)
#     plt.xlabel('根据可预测性排序的模拟器患者')
#     
#     ax2=ax1.twinx()
#     data[str(time_hotizon//time_interval-1)].plot(ax=ax2,style='r',label='30 minutes')
#     # ax2.set_yticks(np.arange(13,35,2))
#     ax2.set_ylabel(metric)
#     plt.legend(loc=1)
#     
#     # plt.grid()
#     plt.title(f'{time_hotizon}分钟的{metric}比较')
#     plt.savefig(f'figure/all_data/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')

def get_predict_metric(data_type='simulator_data', metric='rmse', time_hotizon=60, time_interval=5, model_type='GRU'):
    source_path = f'{data_path}/predictability/result'  # 修复：使用正确路径
    data_path_csv = f'{source_path}/{data_type}_int.csv'
    data = pd.read_csv(data_path_csv, index_col=0, converters={'patient': str})
    result_path = f'{data_path}/result/one_step'  # 修复：使用正确路径
    result_data_path = f'{result_path}/{data_type}_epoch_1000'
    
    gru_data = None
    for patient in os.listdir(result_data_path):
        gru_path = f'{result_data_path}/{patient}/{model_type}.pkl'
        gru = joblib.load(gru_path)[f'{time_hotizon}minuts']
        gru = pd.DataFrame({'patient': [patient], metric: [gru[4]]})
        if gru_data is None:
            gru_data = gru
        else:
            gru_data = pd.concat([gru_data, gru], ignore_index=True)  # 修复：使用concat
    
    data = pd.merge(data, gru_data, how='left', on='patient')
    data = data.sort_values('predictability', ascending=True).reset_index(drop=True)
    data['predictability'] = 1 - data['predictability']
    print(data)
    
    fig = plt.figure(figsize=(8,6))
    ax1 = fig.add_subplot(111)
    data['predictability'].plot(ax=ax1, style='b', label='predictability')
    ax1.set_ylabel("可预测性")
    plt.legend(loc=2)
    plt.xlabel('根据可预测性排序的患者')
    
    ax2 = ax1.twinx()
    data[metric].plot(ax=ax2, style='r', label=f'{time_hotizon} minutes')
    ax2.set_ylabel(metric)
    plt.legend(loc=1)
    
    plt.title(f'{time_hotizon}分钟的{metric}比较-{model_type}')
    plt.savefig(f'{data_path}/predictability/figure/one_data/{model_type}/{data_type}_{metric}_{time_hotizon}.png', dpi=400, bbox_inches='tight')

def get_multi_model(data_type='simulator_data',metric='rmse',time_hotizon=30,time_interval=5):
    source_path=r'e:\study\实习\benchmark\predictability\result'
    data_path=f'{source_path}/{data_type}_int.csv'
    data=pd.read_csv(data_path,index_col=0,converters={'patient':str} )
    data['predictability']=1-data['predictability']
    data=data.sort_values('predictability',ascending=False).reset_index(drop=True)

    result_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\result\one_step'
    result_data_path=f'{result_path}/{data_type}_30'
    ridge_data=None
    svr_data=None
    RF_data=None
    gru_data=None
    for patient in os.listdir(result_data_path):
        gru_path=f'{result_data_path}/{patient}/Ridge.pkl'
        gru=joblib.load(gru_path)
        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})
        if ridge_data is None:
            ridge_data=gru
        else:
            ridge_data=ridge_data.append(gru)
    for patient in os.listdir(result_data_path):
        gru_path=f'{result_data_path}/{patient}/SVR.pkl'
        gru=joblib.load(gru_path)
        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})
        if svr_data is None:
            svr_data=gru
        else:
            svr_data=svr_data.append(gru)  
    for patient in os.listdir(result_data_path):
        gru_path=f'{result_data_path}/{patient}/RF.pkl'
        gru=joblib.load(gru_path)
        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})
        if RF_data is None:
            RF_data=gru
        else:
            RF_data=RF_data.append(gru)
            
    result_path=r'D:\MyWorks\糖尿病预测\blood_glucose_prediction\result\multi_step'
    result_data_path=f'{result_path}/{data_type}_epoch_1000'
    for patient in os.listdir(result_data_path):
        gru_path=f'{result_data_path}/{patient}/GRU.pkl'
        gru=joblib.load(gru_path)
        gru=pd.DataFrame({'patient':[patient],metric:[gru[4][1]/100]})
        if gru_data is None:
            gru_data=gru
        else:
            gru_data=gru_data.append(gru)
            
    merge_gru=pd.merge(data,gru_data,how='left',on='patient')
    merge_ridge=pd.merge(data,ridge_data,how='left',on='patient')
    merge_svr=pd.merge(data,svr_data,how='left',on='patient')
    merge_rf=pd.merge(data,RF_data,how='left',on='patient')
    print(merge_gru)
    print(merge_ridge)
    fig=plt.figure(figsize=(8,6))
    ax1=fig.add_subplot(111)
    data['predictability'].plot(ax=ax1,style='r--',label='predictability',lw=3)
    ax1.set_ylabel("可预测性")
    # ax1.set_yticks(np.arange(0.8,0.9,0.01))
    plt.legend(loc=2)
    plt.xlabel('根据可预测性排序的模拟器患者')
    
    # ax2=ax1.twinx()
    merge_ridge['smape'].plot(ax=ax1,style='b',label='ridge',lw=3)
    merge_gru['smape'].plot(ax=ax1,label='gru',lw=3)
    merge_svr['smape'].plot(ax=ax1,label='SVR',lw=3)
    merge_rf['smape'].plot(ax=ax1,label='RF',lw=3)
    # ax2.set_yticks(np.arange(13,35,2))
    # ax2.set_ylabel(metric)
    plt.legend(loc=1)
    
    plt.grid()
    plt.title(f'{time_hotizon}分钟的{metric}比较-{data_type}')
    plt.savefig(f'figure/compare_figure/{data_type}_{metric}_{time_hotizon}.png',dpi=400,bbox_inches='tight')

metric='smape'
# get_predict_metric('simulator_data',metric,5)
get_predict_metric('simulator_data',metric,30,model_type='GRU')
# get_predict_metric('simulator_data',metric,60)
# get_predict_metric('ohio_data',metric,5)
get_predict_metric('ohio_data',metric,30,model_type='GRU')
# get_predict_metric('ohio_data',metric,60)
# get_predict_metric('hospital_data',metric,15,15)
get_predict_metric('hospital_data',metric,30,15,model_type='GRU')
# get_predict_metric('hospital_data',metric,60,15)

# get_multi_model('simulator_data',metric,5)
get_multi_model('simulator_data',metric,30)
# get_multi_model('simulator_data',metric,60)
# get_multi_model('ohio_data',metric,5)
get_multi_model('ohio_data',metric,30)
# get_multi_model('ohio_data',metric,60)
# get_multi_model('hospital_data',metric,15,15)
get_multi_model('hospital_data',metric,30,15)
# get_multi_model('hospital_data',metric,60,15)


import os
import pandas as pd
for d in os.listdir('result'):
    data=pd.read_csv(f'result/{d}',index_col=0)['predictability']
    print(d,data.describe())


